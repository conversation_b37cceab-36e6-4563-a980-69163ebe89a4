#!/usr/bin/env python3
"""
快速测试修复效果
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_system_info_function():
    """测试 get_system_info 函数"""
    print("🔍 测试 get_system_info 函数...")
    
    try:
        # 设置环境变量
        os.environ.setdefault('ENVIRONMENT', 'development')
        os.environ.setdefault('DEBUG', 'true')
        
        from ai_gen_hub.api.routers.debug import get_system_info
        
        # 测试多次调用
        for i in range(3):
            print(f"   第 {i+1} 次调用...")
            start_time = time.time()
            
            system_info = get_system_info()
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"   执行时间: {execution_time:.3f}秒")
            print(f"   CPU: {system_info.cpu_percent}%, 内存: {system_info.memory_percent}%")
            
            if execution_time > 0.5:
                print(f"   ⚠️  执行时间过长: {execution_time:.3f}秒")
                return False
        
        print("✅ get_system_info 函数测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_info_endpoint():
    """测试 system/info 端点"""
    print("\n🔍 测试 system/info 端点...")
    
    try:
        from fastapi.testclient import TestClient
        from ai_gen_hub.api.app import create_app
        
        # 创建应用
        app = create_app()
        
        with TestClient(app) as client:
            start_time = time.time()
            
            response = client.get("/debug/api/system/info")
            
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"   响应时间: {response_time:.3f}秒")
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ system/info 端点测试通过")
                return response_time < 1.0  # 应该在1秒内完成
            else:
                print(f"❌ 响应错误: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_test_endpoint_simple():
    """简单测试 test-endpoint 接口"""
    print("\n🔍 测试 test-endpoint 接口...")
    
    try:
        from fastapi.testclient import TestClient
        from ai_gen_hub.api.app import create_app
        
        # 创建应用
        app = create_app()
        
        # 简单的测试数据
        test_data = {
            "url": "/health",
            "method": "GET",
            "headers": {},
            "body": "",
            "params": {}
        }
        
        with TestClient(app) as client:
            print("   发送测试请求...")
            start_time = time.time()
            
            try:
                response = client.post(
                    "/debug/api/test-endpoint",
                    json=test_data,
                    timeout=10  # 10秒超时
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                
                print(f"   响应时间: {response_time:.3f}秒")
                print(f"   状态码: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    success = data.get('success', False)
                    print(f"   测试结果: {'成功' if success else '失败'}")
                    
                    if not success:
                        print(f"   错误: {data.get('error', 'Unknown')}")
                    
                    print("✅ test-endpoint 接口测试通过")
                    return True
                else:
                    print(f"❌ 响应错误: {response.text}")
                    return False
                    
            except Exception as e:
                end_time = time.time()
                response_time = end_time - start_time
                
                if "timeout" in str(e).lower() or response_time >= 9:
                    print(f"❌ 请求超时 (耗时: {response_time:.3f}秒)")
                    return False
                else:
                    print(f"❌ 请求异常: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 快速测试修复效果...")
    print("=" * 50)
    
    results = {}
    
    # 测试1: get_system_info 函数
    results['system_info_function'] = test_system_info_function()
    
    # 测试2: system/info 端点
    results['system_info_endpoint'] = test_system_info_endpoint()
    
    # 测试3: test-endpoint 接口
    results['test_endpoint'] = test_test_endpoint_simple()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 所有测试通过！修复成功！")
    else:
        print("\n💥 部分测试失败，需要进一步修复。")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
